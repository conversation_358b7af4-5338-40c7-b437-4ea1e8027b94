{"meetingRecordModel": [{"name": "meetingDate", "type": "date", "descriptionWhatItMeans": "Date when the insurance meeting took place", "label": "<PERSON><PERSON>", "exampleValue": "03.06.2025", "valueFromContract": ""}, {"name": "meetingTime", "type": "string", "descriptionWhatItMeans": "Time when the insurance meeting started", "label": "Čas jednání", "exampleValue": "14:30", "valueFromContract": ""}, {"name": "meetingLocation", "type": "string", "descriptionWhatItMeans": "Location where the meeting took place", "label": "<PERSON><PERSON><PERSON>", "exampleValue": "Pobočka Praha 1, Wenceslas Square 10", "valueFromContract": ""}, {"name": "<PERSON><PERSON><PERSON>", "type": "string", "descriptionWhatItMeans": "Full name of the insurance advisor conducting the meeting", "label": "Jméno poradce", "exampleValue": "<PERSON><PERSON><PERSON> <PERSON>", "valueFromContract": ""}, {"name": "advisorId", "type": "string", "descriptionWhatItMeans": "Identification number or code of the advisor", "label": "ID poradce", "exampleValue": "ADV12345", "valueFromContract": ""}, {"name": "clientType", "type": "string", "descriptionWhatItMeans": "Type of client - individual person or legal entity", "label": "<PERSON><PERSON>", "exampleValue": "FO", "valueFromContract": ""}, {"name": "clientName", "type": "string", "descriptionWhatItMeans": "Full name of the client", "label": "Jméno klienta", "exampleValue": "<PERSON>", "valueFromContract": ""}, {"name": "clientBirthNumber", "type": "string", "descriptionWhatItMeans": "Birth number or company identification number of the client", "label": "<PERSON><PERSON><PERSON>/IČO", "exampleValue": "8506151234", "valueFromContract": ""}, {"name": "clientAddress", "type": "string", "descriptionWhatItMeans": "Full address of the client", "label": "<PERSON><PERSON><PERSON>", "exampleValue": "<PERSON><PERSON><PERSON><PERSON> 123, 110 00 Praha 1", "valueFromContract": ""}, {"name": "clientPhone", "type": "string", "descriptionWhatItMeans": "Contact phone number of the client", "label": "Telefon klienta", "exampleValue": "+*********** 789", "valueFromContract": ""}, {"name": "clientEmail", "type": "string", "descriptionWhatItMeans": "Email address of the client", "label": "<PERSON><PERSON>", "exampleValue": "jan.svo<PERSON><PERSON>@email.cz", "valueFromContract": ""}, {"name": "vehicleMake", "type": "string", "descriptionWhatItMeans": "Make/brand of the vehicle to be insured", "label": "Značka vozidla", "exampleValue": "Škoda", "valueFromContract": ""}, {"name": "vehicleModel", "type": "string", "descriptionWhatItMeans": "Model of the vehicle to be insured", "label": "<PERSON> vozidla", "exampleValue": "Octavia", "valueFromContract": ""}, {"name": "vehicleYear", "type": "string", "descriptionWhatItMeans": "Year of manufacture of the vehicle", "label": "Rok výroby", "exampleValue": "2022", "valueFromContract": ""}, {"name": "vehicleVin", "type": "string", "descriptionWhatItMeans": "Vehicle identification number", "label": "VIN", "exampleValue": "TMBJF25L1C1234567", "valueFromContract": ""}, {"name": "vehicleLicensePlate", "type": "string", "descriptionWhatItMeans": "License plate number of the vehicle", "label": "SPZ", "exampleValue": "1AB 2345", "valueFromContract": ""}, {"name": "vehicleEngineVolume", "type": "string", "descriptionWhatItMeans": "Engine volume in cubic centimeters", "label": "<PERSON><PERSON><PERSON><PERSON>", "exampleValue": "1968 cm³", "valueFromContract": ""}, {"name": "vehicleEnginePower", "type": "string", "descriptionWhatItMeans": "Engine power in kilowatts", "label": "Výkon motoru", "exampleValue": "110 kW", "valueFromContract": ""}, {"name": "vehicleValue", "type": "string", "descriptionWhatItMeans": "Current market value of the vehicle", "label": "Hodnota vozidla", "exampleValue": "650 000 Kč", "valueFromContract": ""}, {"name": "vehicleUsage", "type": "string", "descriptionWhatItMeans": "Purpose of vehicle usage", "label": "Účel použití", "exampleValue": "B<PERSON>ž<PERSON><PERSON>", "valueFromContract": ""}, {"name": "insuranceType", "type": "string", "descriptionWhatItMeans": "Type of vehicle insurance discussed", "label": "Typ pojištění", "exampleValue": "Povinné ruč<PERSON>í + Havarijn<PERSON> pojištění", "valueFromContract": ""}, {"name": "liabilityInsuranceLimit", "type": "string", "descriptionWhatItMeans": "Coverage limit for liability insurance", "label": "<PERSON>it pov<PERSON> ručení", "exampleValue": "150/150 mil. Kč", "valueFromContract": ""}, {"name": "collisionInsuranceValue", "type": "string", "descriptionWhatItMeans": "Insured value for collision coverage", "label": "Pojistná částka havarijního pojištění", "exampleValue": "650 000 Kč", "valueFromContract": ""}, {"name": "deductible", "type": "string", "descriptionWhatItMeans": "Deductible amount or percentage for collision insurance", "label": "Spoluúčast", "exampleValue": "5%, min. 5 000 Kč", "valueFromContract": ""}, {"name": "bonusMalus", "type": "string", "descriptionWhatItMeans": "Bonus/malus percentage for liability insurance", "label": "Bonus/malus", "exampleValue": "50%", "valueFromContract": ""}, {"name": "assistanceServices", "type": "string", "descriptionWhatItMeans": "Type of assistance services included", "label": "Asistenční s<PERSON>", "exampleValue": "Základní asistence ČR + Evropa", "valueFromContract": ""}, {"name": "additionalCoverages", "type": "array", "descriptionWhatItMeans": "List of additional insurance coverages discussed", "label": "Doplňková připojištění", "exampleValue": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> skel, GAP pojištění", "valueFromContract": ""}, {"name": "annualPremiumLiability", "type": "string", "descriptionWhatItMeans": "Annual premium for liability insurance", "label": "Roční pojistné - povinné ručení", "exampleValue": "4 500 Kč", "valueFromContract": ""}, {"name": "annualPremiumCollision", "type": "string", "descriptionWhatItMeans": "Annual premium for collision insurance", "label": "Roční pojistné - havarijní pojištění", "exampleValue": "8 200 Kč", "valueFromContract": ""}, {"name": "totalAnnualPremium", "type": "string", "descriptionWhatItMeans": "Total annual premium for all coverages", "label": "Celkové roční pojistné", "exampleValue": "13 500 Kč", "valueFromContract": ""}, {"name": "paymentFrequency", "type": "string", "descriptionWhatItMeans": "Frequency of premium payments", "label": "Frekvence platby", "exampleValue": "Ročně", "valueFromContract": ""}, {"name": "paymentMethod", "type": "string", "descriptionWhatItMeans": "Method of premium payment", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON> plat<PERSON>", "exampleValue": "SIPO", "valueFromContract": ""}, {"name": "insuranceStart", "type": "date", "descriptionWhatItMeans": "Start date of insurance coverage", "label": "Začátek pojištění", "exampleValue": "01.07.2025", "valueFromContract": ""}, {"name": "previousInsurer", "type": "string", "descriptionWhatItMeans": "Name of previous insurance company", "label": "Předchozí pojišťovna", "exampleValue": "Generali Pojišťovna a.s.", "valueFromContract": ""}, {"name": "claimsHistory", "type": "string", "descriptionWhatItMeans": "Information about previous insurance claims", "label": "Historie š<PERSON>d", "exampleValue": "Bez škod za posledních 5 let", "valueFromContract": ""}, {"name": "clientNeeds", "type": "string", "descriptionWhatItMeans": "Specific needs and requirements expressed by the client", "label": "Potřeby klienta", "exampleValue": "Komplexní krytí s nízkou spoluúčastí", "valueFromContract": ""}, {"name": "recommendedProduct", "type": "string", "descriptionWhatItMeans": "Insurance product recommended by the advisor", "label": "Doporučený produkt", "exampleValue": "Autopojištění Premium", "valueFromContract": ""}, {"name": "reasonForRecommendation", "type": "string", "descriptionWhatItMeans": "Explanation why this product was recommended", "label": "Důvod doporučení", "exampleValue": "Nejlepš<PERSON> poměr cena/výkon pro klientovy potřeby", "valueFromContract": ""}, {"name": "alternativeOptions", "type": "string", "descriptionWhatItMeans": "Alternative insurance options presented to the client", "label": "Alternativní možnosti", "exampleValue": "Autopojištění Standard s vyšší spoluúčastí", "valueFromContract": ""}, {"name": "clientDecision", "type": "string", "descriptionWhatItMeans": "Final decision made by the client", "label": "Rozhodnutí klienta", "exampleValue": "Přijal doporučený produkt", "valueFromContract": ""}, {"name": "nextSteps", "type": "string", "descriptionWhatItMeans": "Agreed next steps in the insurance process", "label": "<PERSON><PERSON><PERSON> k<PERSON>", "exampleValue": "Zaslání návrhu smlouvy do 3 dnů", "valueFromContract": ""}, {"name": "documentsProvided", "type": "array", "descriptionWhatItMeans": "List of documents provided to the client", "label": "Předané <PERSON>", "exampleValue": "Informační dokument o pojistném produktu, Všeobecné pojistné <PERSON>ínky", "valueFromContract": ""}, {"name": "documentsRequired", "type": "array", "descriptionWhatItMeans": "List of documents required from the client", "label": "Požadované dokumenty", "exampleValue": "Technický p<PERSON>ů<PERSON>, Potvrzení o průběhu pojištění", "valueFromContract": ""}, {"name": "meetingDuration", "type": "string", "descriptionWhatItMeans": "Duration of the meeting in minutes", "label": "<PERSON><PERSON><PERSON>a j<PERSON>ná<PERSON>í", "exampleValue": "45 minut", "valueFromContract": ""}, {"name": "meetingOutcome", "type": "string", "descriptionWhatItMeans": "Overall outcome of the meeting", "label": "<PERSON><PERSON><PERSON>dek jednání", "exampleValue": "Úspěšné - klient má zájem o pojištění", "valueFromContract": ""}, {"name": "followUpDate", "type": "date", "descriptionWhatItMeans": "Date for follow-up contact with the client", "label": "<PERSON><PERSON> n<PERSON> k<PERSON>", "exampleValue": "06.06.2025", "valueFromContract": ""}, {"name": "advisorNotes", "type": "string", "descriptionWhatItMeans": "Additional notes and observations from the advisor", "label": "Poznámky poradce", "exampleValue": "<PERSON>lient je velmi opatrný řidič, vhodný pro slevy", "valueFromContract": ""}, {"name": "clientSignature", "type": "boolean", "descriptionWhatItMeans": "Whether the client signed the meeting record", "label": "Pod<PERSON> k<PERSON>a", "exampleValue": "true", "valueFromContract": ""}, {"name": "advisorSignature", "type": "boolean", "descriptionWhatItMeans": "Whether the advisor signed the meeting record", "label": "Podpis p<PERSON>", "exampleValue": "true", "valueFromContract": ""}]}