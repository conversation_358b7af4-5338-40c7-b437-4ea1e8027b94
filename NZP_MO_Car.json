{
  "meetingRecordModel": {
    "client": {
      "name": "client",
      "type": "object",
      "descriptionWhatItMeans": "Informace o hlavním klientovi",
      "label": "Informace o klientovi",
      "fields": [
        {
          "name": "naturalPerson",
          "type": "object",
          "descriptionWhatItMeans": "Údaje pro fyzickou osobu",
          "label": "Fyzická osoba",
          "fields": [
            {
              "name": "firtName",
              "type": "string",
              "descriptionWhatItMeans": "Jméno fyzické osoby",
              "label": "Jméno",
              "exampleValue": "Jan",
              "valueFromContract": ""
            },
            {
              "name": "lastName",
              "type": "string",
              "descriptionWhatItMeans": "Příjmení fyzické osoby",
              "label": "Příjmení",
              "exampleValue": "Svoboda",
              "valueFromContract": ""
            },
            {
              "name": "birthNumber",
              "type": "string",
              "descriptionWhatItMeans": "Rodné č<PERSON>lo fyzick<PERSON> osoby",
              "label": "<PERSON>n<PERSON> č<PERSON>lo",
              "exampleValue": "8506151234",
              "valueFromContract": ""
            },
            {
              "name": "birthDate",
              "type": "string",
              "descriptionWhatItMeans": "Datum narození fyzické osoby",
              "label": "Datum narození",
              "exampleValue": "1985-06-15",
              "valueFromContract": ""
            },
            {
              "name": "phone",
              "type": "string",
              "descriptionWhatItMeans": "Telefonní číslo fyzické osoby",
              "label": "Telefon",
              "exampleValue": "+*********** 789",
              "valueFromContract": ""
            },
            {
              "name": "email",
              "type": "string",
              "descriptionWhatItMeans": "Emailová adresa fyzické osoby",
              "label": "Email",
              "exampleValue": "<EMAIL>",
              "valueFromContract": ""
            },
            {
              "name": "address",
              "type": "object",
              "descriptionWhatItMeans": "Adresa trvalého bydliště fyzické osoby rozčleněná do jednotlivých polí",
              "label": "Adresa trvalého bydliště",
              "fields": [
                {
                  "name": "street",
                  "type": "string",
                  "descriptionWhatItMeans": "Název ulice",
                  "label": "Ulice",
                  "exampleValue": "Dlouhá",
                  "valueFromContract": ""
                },
                {
                  "name": "landRegistryNumber",
                  "type": "string",
                  "descriptionWhatItMeans": "Číslo popisné/orientační domu",
                  "label": "Číslo popisné",
                  "exampleValue": "23",
                  "valueFromContract": ""
                },
                {
                  "name": "houseNumber",
                  "type": "string",
                  "descriptionWhatItMeans": "Číslo popisné/orientační domu",
                  "label": "Číslo orientační",
                  "exampleValue": "123",
                  "valueFromContract": ""
                },
                {
                  "name": "city",
                  "type": "string",
                  "descriptionWhatItMeans": "Město nebo obec",
                  "label": "Obec",
                  "exampleValue": "Praha 1 - Staré město",
                  "valueFromContract": ""
                },
                {
                  "name": "zip",
                  "type": "string",
                  "descriptionWhatItMeans": "PSČ (poštovní směrovací číslo)",
                  "label": "PSČ",
                  "exampleValue": "11000",
                  "valueFromContract": ""
                },
                {
                  "name": "country",
                  "type": "string",
                  "descriptionWhatItMeans": "Země",
                  "label": "Stát",
                  "exampleValue": "Česko",
                  "valueFromContract": ""
                }
              ]
            }
          ]
        },
        {
          "name": "selfEmployedPerson",
          "type": "object",
          "descriptionWhatItMeans": "Údaje pro fyzickou osobu podnikající",
          "label": "Fyzická osoba podnikající",
          "fields": [
            {
              "name": "firtName",
              "type": "string",
              "descriptionWhatItMeans": "Jméno fyzické osoby podnikající",
              "label": "Jméno",
              "exampleValue": "Jan",
              "valueFromContract": ""
            },
            {
              "name": "lastName",
              "type": "string",
              "descriptionWhatItMeans": "Příjmení fyzické osoby podnikající",
              "label": "Příjmení",
              "exampleValue": "Svoboda",
              "valueFromContract": ""
            },
            {
              "name": "companyNumber",
              "type": "string",
              "descriptionWhatItMeans": "IČ (živnostenský rejstřík nebo IČO podnikatele)",
              "label": "IČO",
              "exampleValue": "06482619",
              "valueFromContract": ""
            },
            {
              "name": "birthDate",
              "type": "string",
              "descriptionWhatItMeans": "Datum narození fyzické osoby podnikající",
              "label": "Datum narození",
              "exampleValue": "1985-06-15",
              "valueFromContract": ""
            },
            {
              "name": "phone",
              "type": "string",
              "descriptionWhatItMeans": "Telefonní číslo fyzické osoby podnikající",
              "label": "Telefon",
              "exampleValue": "+*********** 789",
              "valueFromContract": ""
            },
            {
              "name": "email",
              "type": "string",
              "descriptionWhatItMeans": "Emailová adresa fyzické osoby podnikající",
              "label": "Email",
              "exampleValue": "<EMAIL>",
              "valueFromContract": ""
            },
            {
              "name": "address",
              "type": "object",
              "descriptionWhatItMeans": "Adresa místa podnikání rozčleněná do jednotlivých polí",
              "label": "Adresa místa podnikání",
              "fields": [
                {
                  "name": "street",
                  "type": "string",
                  "descriptionWhatItMeans": "Název ulice",
                  "label": "Ulice",
                  "exampleValue": "Dlouhá",
                  "valueFromContract": ""
                },
                {
                  "name": "landRegistryNumber",
                  "type": "string",
                  "descriptionWhatItMeans": "Číslo popisné/orientační domu",
                  "label": "Číslo popisné",
                  "exampleValue": "23",
                  "valueFromContract": ""
                },
                {
                  "name": "houseNumber",
                  "type": "string",
                  "descriptionWhatItMeans": "Číslo popisné/orientační domu",
                  "label": "Číslo orientační",
                  "exampleValue": "123",
                  "valueFromContract": ""
                },
                {
                  "name": "city",
                  "type": "string",
                  "descriptionWhatItMeans": "Město nebo obec",
                  "label": "Obec",
                  "exampleValue": "Praha 1 - Staré město",
                  "valueFromContract": ""
                },
                {
                  "name": "zip",
                  "type": "string",
                  "descriptionWhatItMeans": "PSČ (poštovní směrovací číslo)",
                  "label": "PSČ",
                  "exampleValue": "11000",
                  "valueFromContract": ""
                },
                {
                  "name": "country",
                  "type": "string",
                  "descriptionWhatItMeans": "Země",
                  "label": "Stát",
                  "exampleValue": "Česko",
                  "valueFromContract": ""
                }
              ]
            }
          ]
        },
        {
          "name": "legalEntity",
          "type": "object",
          "descriptionWhatItMeans": "Údaje pro právnickou osobu",
          "label": "Právnická osoba",
          "fields": [
            {
              "name": "companyNumber",
              "type": "string",
              "descriptionWhatItMeans": "IČ (živnostenský rejstřík nebo IČO právnické osoby)",
              "label": "IČO",
              "exampleValue": "06482619",
              "valueFromContract": ""
            },
            {
              "name": "companyName",
              "type": "string",
              "descriptionWhatItMeans": "Obchodní název nebo název společnosti právnické osoby",
              "label": "Název",
              "exampleValue": "Acme s.r.o.",
              "valueFromContract": ""
            },
            {
              "name": "phone",
              "type": "string",
              "descriptionWhatItMeans": "Telefonní číslo právnické osoby",
              "label": "Telefon klienta",
              "exampleValue": "+*********** 789",
              "valueFromContract": ""
            },
            {
              "name": "email",
              "type": "string",
              "descriptionWhatItMeans": "Emailová adresa právnické osoby",
              "label": "Email klienta",
              "exampleValue": "<EMAIL>",
              "valueFromContract": ""
            },
            {
              "name": "address",
              "type": "object",
              "descriptionWhatItMeans": "Adresa sídla rozčleněná do jednotlivých polí",
              "label": "Adresa sídla",
              "fields": [
                {
                  "name": "street",
                  "type": "string",
                  "descriptionWhatItMeans": "Název ulice",
                  "label": "Ulice",
                  "exampleValue": "Dlouhá",
                  "valueFromContract": ""
                },
                {
                  "name": "landRegistryNumber",
                  "type": "string",
                  "descriptionWhatItMeans": "Číslo popisné/orientační domu",
                  "label": "Číslo popisné",
                  "exampleValue": "23",
                  "valueFromContract": ""
                },
                {
                  "name": "houseNumber",
                  "type": "string",
                  "descriptionWhatItMeans": "Číslo popisné/orientační domu",
                  "label": "Číslo orientační",
                  "exampleValue": "123",
                  "valueFromContract": ""
                },
                {
                  "name": "city",
                  "type": "string",
                  "descriptionWhatItMeans": "Město nebo obec",
                  "label": "Obec",
                  "exampleValue": "Praha 1 - Staré město",
                  "valueFromContract": ""
                },
                {
                  "name": "zip",
                  "type": "string",
                  "descriptionWhatItMeans": "PSČ (poštovní směrovací číslo)",
                  "label": "PSČ",
                  "exampleValue": "11000",
                  "valueFromContract": ""
                },
                {
                  "name": "country",
                  "type": "string",
                  "descriptionWhatItMeans": "Země",
                  "label": "Stát",
                  "exampleValue": "Česko",
                  "valueFromContract": ""
                }
              ]
            }
          ]
        }
      ]
    },
    "car": {
      "name": "car",
      "type": "object",
      "descriptionWhatItMeans": "Informace o pojišťovaném vozidle klienta",
      "label": "Informace o vozidle",
      "fields": [
        {
          "name": "identification",
          "type": "string",
          "descriptionWhatItMeans": "Registrační nebo poznávací značka / VIN / Identifikátor vozidla",
          "label": "RZ / VIN / Identifikátor",
          "exampleValue": "TMBJF25L1C1234567",
          "valueFromContract": ""
        },
        {
          "name": "brand",
          "type": "string",
          "descriptionWhatItMeans": "Tovární značka vozidla",
          "label": "Tovární značka",
          "exampleValue": "Toyota",
          "valueFromContract": ""
        },
        {
          "name": "model",
          "type": "string",
          "descriptionWhatItMeans": "Model vozidla",
          "label": "Model",
          "exampleValue": "CH-R",
          "valueFromContract": ""
        },
        {
          "name": "firtsRegistration",
          "type": "string",
          "descriptionWhatItMeans": "Datum první registrace / uvedení vozidla do provozu",
          "label": "Datum uvedení do provozu",
          "exampleValue": "2018-06-03",
          "valueFromContract": ""
        },
        {
          "name": "yearKm",
          "type": "int",
          "descriptionWhatItMeans": "Průměrný roční nájezd v kilometrech",
          "label": "Roční nájezd v km",
          "exampleValue": "20000",
          "valueFromContract": ""
        }
      ]
    },
    "clientNeeds": {
      "name": "clientNeeds",
      "type": "object",
      "descriptionWhatItMeans": "Specifické požadavky a potřeby vyjádřené klientem",
      "label": "Požadavky a potřeby klienta",
      "fields": [
        {
          "name": "pov",
          "type": "object",
          "descriptionWhatItMeans": "Údaje k povinnému ručení a limitům",
          "label": "Povinné ručení",
          "fields": [
            {
              "name": "pov",
              "type": "boolean",
              "descriptionWhatItMeans": "Zda klient požaduje povinné ručení",
              "label": "Povinné ručení",
              "exampleValue": true,
              "valueFromContract": ""
            },
            {
              "name": "limityType",
              "type": "string",
              "descriptionWhatItMeans": "Hodnota vybraných limitů povinného ručení",
              "label": "Limity",
              "exampleValue": "Vysoké (150 mil Kč a více)",
              "valueFromContract": ""
            }
          ]
        },
        {
          "name": "hav",
          "type": "object",
          "descriptionWhatItMeans": "Údaje k havarijnímu pojištění, spoluúčasti a ceně vozidla",
          "label": "Havarijní pojištění",
          "fields": [
            {
              "name": "hav",
              "type": "boolean",
              "descriptionWhatItMeans": "Zda klient požaduje havarijní pojištění",
              "label": "Havarijní pojištění",
              "exampleValue": false,
              "valueFromContract": ""
            },
            {
              "name": "excessRange",
              "type": "string",
              "descriptionWhatItMeans": "Výše spoluúčasti u havarijního pojištění",
              "label": "Spoluúčast",
              "exampleValue": "5.000 Kč nebo 5% / min. 5.000 Kč",
              "valueFromContract": ""
            },
            {
              "name": "vehiclePrice",
              "type": "integer",
              "descriptionWhatItMeans": "Orientační cena vozidla s DPH",
              "label": "Orientační cena vozidla s DPH",
              "exampleValue": 500000,
              "valueFromContract": ""
            },
            {
              "name": "supplementaryInsurances",
              "type": "object",
              "descriptionWhatItMeans": "Jednotlivá požadovaná rizika k havarijnímu pojištění",
              "label": "Požadovaná rizika",
              "fields": [
                {
                  "name": "allRisk",
                  "type": "boolean",
                  "descriptionWhatItMeans": "ALLRISK - zahrnutí všech rizik",
                  "label": "ALLRISK",
                  "exampleValue": false,
                  "valueFromContract": ""
                },
                {
                  "name": "havarie",
                  "type": "boolean",
                  "descriptionWhatItMeans": "Riziko havárie",
                  "label": "Havárie",
                  "exampleValue": true,
                  "valueFromContract": ""
                },
                {
                  "name": "zivel",
                  "type": "boolean",
                  "descriptionWhatItMeans": "Riziko živelných pohrom",
                  "label": "Živel",
                  "exampleValue": false,
                  "valueFromContract": ""
                },
                {
                  "name": "stretSeZveri",
                  "type": "boolean",
                  "descriptionWhatItMeans": "Riziko střetu se zvěří",
                  "label": "Střet se zvěří",
                  "exampleValue": true,
                  "valueFromContract": ""
                },
                {
                  "name": "odcizeni",
                  "type": "boolean",
                  "descriptionWhatItMeans": "Riziko odcizení vozidla",
                  "label": "Odcizení",
                  "exampleValue": false,
                  "valueFromContract": ""
                },
                {
                  "name": "vandalismus",
                  "type": "boolean",
                  "descriptionWhatItMeans": "Riziko vandalismu",
                  "label": "Vandalismus",
                  "exampleValue": false,
                  "valueFromContract": ""
                },
                {
                  "name": "poskozeniZviretem",
                  "type": "boolean",
                  "descriptionWhatItMeans": "Riziko poškození zvířetem",
                  "label": "Poškození zvířetem",
                  "exampleValue": false,
                  "valueFromContract": ""
                }
              ]
            }
          ]
        },
        {
          "name": "supplementaryInsurances",
          "type": "object",
          "descriptionWhatItMeans": "Jednotlivá požadovaná připojištění",
          "label": "Připojištění",
          "fields": [
            {
              "name": "urazovePojisteniRidice",
              "type": "boolean",
              "descriptionWhatItMeans": "Úrazové pojištění řidiče a/nebo cestujících",
              "label": "Úrazové pojištění řidiče / cestujících",
              "exampleValue": true,
              "valueFromContract": ""
            },
            {
              "name": "zavazadla",
              "type": "boolean",
              "descriptionWhatItMeans": "Pojištění zavazadel",
              "label": "Zavazadla",
              "exampleValue": false,
              "valueFromContract": ""
            },
            {
              "name": "primaLikvidace",
              "type": "boolean",
              "descriptionWhatItMeans": "Přímá likvidace škody",
              "label": "Přímá likvidace",
              "exampleValue": false,
              "valueFromContract": ""
            },
            {
              "name": "nahradniVozidlo",
              "type": "boolean",
              "descriptionWhatItMeans": "Pojištění náhradního vozidla",
              "label": "Náhradní vozidlo",
              "exampleValue": true,
              "valueFromContract": ""
            },
            {
              "name": "pojisteniSkel",
              "type": "boolean",
              "descriptionWhatItMeans": "Pojištění skel",
              "label": "Pojištění skel",
              "exampleValue": true,
              "valueFromContract": ""
            },
            {
              "name": "pojisteniSkelLimit",
              "type": "integer",
              "descriptionWhatItMeans": "Limit pro pojištění skel (v Kč)",
              "label": "Limit (Kč) – Poj. skel",
              "exampleValue": 20000,
              "valueFromContract": ""
            },
            {
              "name": "rozsirenaAsistence",
              "type": "boolean",
              "descriptionWhatItMeans": "Rozšířená asistenční služba",
              "label": "Rozšířená asistence",
              "exampleValue": false,
              "valueFromContract": ""
            },
            {
              "name": "GAP",
              "type": "boolean",
              "descriptionWhatItMeans": "GAP pojištění (doplatek do plné hodnoty vozidla)",
              "label": "GAP",
              "exampleValue": false,
              "valueFromContract": ""
            },
            {
              "name": "GAPLimit",
              "type": "integer",
              "descriptionWhatItMeans": "Limit pro GAP pojištění (v Kč)",
              "label": "Limit (Kč) – GAP",
              "exampleValue": 100000,
              "valueFromContract": ""
            },
            {
              "name": "garanceCeny",
              "type": "boolean",
              "descriptionWhatItMeans": "Garance ceny vozidla",
              "label": "Garance ceny",
              "exampleValue": false,
              "valueFromContract": ""
            }
          ]
        },
        {
          "name": "dalsiPozadavky",
          "type": "string",
          "descriptionWhatItMeans": "Dodatečné požadavky nebo upřesnění od klienta",
          "label": "Další požadavky nebo upřesnění požadavků",
          "exampleValue": "",
          "valueFromContract": ""
        }
      ]
    },
    "advisorRecommendation": {
      "name": "advisorRecommendation",
      "type": "object",
      "descriptionWhatItMeans": "Doporučení poradce",
      "label": "Doporučení poradce",
      "fields": [
        {
          "name": "contractType",
          "type": "string",
          "descriptionWhatItMeans": "Typ smluvní aktivity / smlouvy",
          "label": "Typ smluvní aktivity ",
          "exampleValue": "Nová smlouva / náhrada smlouvy",
          "valueFromContract": ""
        },
        {
          "name": "producer",
          "type": "string",
          "descriptionWhatItMeans": "Název pojišťovny / producenta",
          "label": "Pojišťovna",
          "exampleValue": "Česká podnikatelská pojišťovna, a.s., Vienna Insurance Group",
          "valueFromContract": ""
        },
        {
          "name": "product",
          "type": "string",
          "descriptionWhatItMeans": "Název produktu pojišťovny / producenta",
          "label": "Produkt",
          "exampleValue": "Pojištění auta",
          "valueFromContract": ""
        },
        {
          "name": "contractSetting",
          "type": "boolean",
          "descriptionWhatItMeans": "Navrhované řešení odpovídá klientovým požadavkům",
          "label": "Nastavení podmínek pojištění",
          "exampleValue": "true",
          "valueFromContract": ""
        },
        {
          "name": "clientRequirementNotification",
          "type": "string",
          "descriptionWhatItMeans": "Upozornění na nesrovnalosti mezi požadavky klienta a navrhovaným pojištěním",
          "label": "Upozornění na nesrovnalosti mezi požadavky klienta a navrhovaným pojištěním",
          "exampleValue": "Klient požaduje pojištění přírodních událostí a poškození zvěří srážkou či okusem.",
          "valueFromContract": ""
        },
        {
          "name": "substantiation",
          "type": "string",
          "descriptionWhatItMeans": "Zdůvodnění poradce k výběru produktu",
          "label": "Zdůvodnění poradce k výběru produktu",
          "exampleValue": "Doporučuji sjednat smlouvu u pojišťovny...",
          "valueFromContract": ""
        },
        {
          "name": "ProposedSolution",
          "type": "boolean",
          "descriptionWhatItMeans": "Klient odmítl / zvolil navrhované řešení",
          "label": "Klient zvolil navrhované řešení",
          "exampleValue": "true",
          "valueFromContract": ""
        }
      ]
    },
    {
      "name": "clientNotification",
      "type": "object",
      "descriptionWhatItMeans": "Upozornění klienta na dopady navrhovaného pojištění",
      "label": "Upozornění klienta na dopady navrhovaného pojištění",
      "fields": [
        {
          "name": "clientImpact",
          "type": "string",
          "descriptionWhatItMeans": "Upozornění klienta na dopady navrhovaného pojištění",
          "label": "Upozornění na dopady navrhovaného pojištění",
          "exampleValue": "V případě, že dojde k prodeji vozidla upozorňuji na nutnost včas oznámit pojišťovně změnu vlastníka vozidla...",
          "valueFromContract": ""
        }
      ]
    },
    {
      "name": "processEvidenceRecord",
      "type": "object",
      "descriptionWhatItMeans": "Evidence procesu řešení pojistných potřeb klienta",
      "label": "Evidence procesu řešení pojistných potřeb klienta",
      "fields": [
        {
          "name": "date1",
          "type": "string",
          "descriptionWhatItMeans": "Upozornění klienta na dopady navrhovaného pojištění",
          "label": "Datum",
          "exampleValue": "2025-03-06",
          "valueFromContract": ""
        },
        {
          "name": "date2",
          "type": "string",
          "descriptionWhatItMeans": "Poradce zjistil požadavky a potřeby klienta dne",
          "label": "Datum",
          "exampleValue": "2025-03-06",
          "valueFromContract": ""
        },
        {
          "name": "date3",
          "type": "string",
          "descriptionWhatItMeans": "Poradce představil klientovi návrh řešení jehopožadavků a potřeb, ze kterých si klient vybral požadované řešení dne",
          "label": "Datum",
          "exampleValue": "2025-03-07",
          "valueFromContract": ""
        },
      ]
    },
    {
      "name": "signatures",
      "type": "object",
      "descriptionWhatItMeans": "Information about signatures on the meeting record",
      "label": "Podpisy",
      "fields": [
        {
          "name": "clientSignature",
          "type": "boolean",
          "descriptionWhatItMeans": "Whether the client signed the meeting record",
          "label": "Podpis klienta",
          "exampleValue": "true",
          "valueFromContract": ""
        },
        {
          "name": "advisorSignature",
          "type": "boolean",
          "descriptionWhatItMeans": "Whether the advisor signed the meeting record",
          "label": "Podpis poradce",
          "exampleValue": "true",
          "valueFromContract": ""
        },
        {
          "name": "signatureDate",
          "type": "date",
          "descriptionWhatItMeans": "Date when the meeting record was signed",
          "label": "Datum podpisu",
          "exampleValue": "03.06.2025",
          "valueFromContract": ""
        },
        {
          "name": "signatureLocation",
          "type": "string",
          "descriptionWhatItMeans": "Location where the meeting record was signed",
          "label": "Místo podpisu",
          "exampleValue": "Praha",
          "valueFromContract": ""
        }
      ]
    },
    {
      "name": "advisor",
      "type": "object",
      "descriptionWhatItMeans": "Information about the insurance advisor conducting the meeting",
      "label": "Poradce",
      "fields": [
        {
          "name": "advisorName",
          "type": "string",
          "descriptionWhatItMeans": "Full name of the insurance advisor conducting the meeting",
          "label": "Jméno poradce",
          "exampleValue": "Ing. Pavel Novák",
          "valueFromContract": ""
        },
        {
          "name": "advisorId",
          "type": "string",
          "descriptionWhatItMeans": "Identification number or code of the advisor",
          "label": "ID poradce",
          "exampleValue": "ADV12345",
          "valueFromContract": ""
        }
      ]
    }
  },
  "contractdraftModel": {}
}