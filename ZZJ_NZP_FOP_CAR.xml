<EPPDocumentData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Id>0</Id>
  <GuidID>09576cd3-53b8-4fe1-8736-01ed88affb4e</GuidID>
  <PdfGuid>297c65ae-9e90-4c87-842b-2fb182d16a8d</PdfGuid>
  <ExternalStoreGuid>d0a9c187-abe2-4aa7-9baf-00d12ae9665c</ExternalStoreGuid>
  <TemplateVersion>1</TemplateVersion>
  <Template>PdfDocument.html</Template>
  <TemplateFooter>PdfFooter.html</TemplateFooter>
  <TemplateCode>ZzjNzp_CAR_FOP</TemplateCode>
  <IncludeTemplateCode>ZzjNzp_CAR_FOP</IncludeTemplateCode>
  <TemplatePartner xsi:nil="true" />
  <IsReadOnly>true</IsReadOnly>
  <Components>
    <EPPBaseComponent xsi:type="EPPHlavicka2">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Záznam z jednání - neživotní pojištění (Fyzická osoba podnikající)</Title>
      <Description>dle zákona č. 170/2018 Sb., o distribuci pojištění a zajištění</Description>
      <NumberTitle>0</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_Hlavicka2</IdName>
      <TemplateCode>Hlavicka2</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppKlientFop">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Informace o klientovi</Title>
      <NumberTitle>1</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_KlientFop</IdName>
      <TemplateCode>KlientFop</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <FirstName>Lenka</FirstName>
      <LastName>Höflerová</LastName>
      <Telephone>+420721111118</Telephone>
      <Email><EMAIL></Email>
      <BirthDate>1991-07-25T00:00:00</BirthDate>
      <TitleAddress>Adresa trvalého bydliště:</TitleAddress>
      <Ulice>Václavské náměstí 295/3</Ulice>
      <Psc>41201</Psc>
      <Obec>Litoměřice - Předměstí</Obec>
      <Stat>Česko</Stat>
      <CompanyName>Lenka Ortinskáá</CompanyName>
      <CompanyIdentificationNumber>06482619</CompanyIdentificationNumber>
      <CompanyTitleAddress>Adresa místa podnikání:</CompanyTitleAddress>
      <CompanyUlice>Václavské náměstí 295/3</CompanyUlice>
      <CompanyPsc>41201</CompanyPsc>
      <CompanyObec>Litoměřice - Předměstí</CompanyObec>
      <CompanyStat>Česko</CompanyStat>
      <IsShortView>false</IsShortView>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPText">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Další pojištěné osoby (starší 18 let)</Title>
      <NumberTitle>2</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_Text</IdName>
      <TemplateCode>Text</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <IsVisbleInPdf>true</IsVisbleInPdf>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPOsoba_v3">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id>Osoba-Adult-1</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_Osoba-v3Osoba-Adult-1</IdName>
      <TemplateCode>Osoba-v3</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <TitleAddress>Adresa trvalého bydliště:</TitleAddress>
      <FirstName>Test</FirstName>
      <LastName>048402</LastName>
      <Telephone>+420777111222</Telephone>
      <Email><EMAIL></Email>
      <IdentificationNumber>0705307757</IdentificationNumber>
      <BirthDate>2007-05-30T00:00:00</BirthDate>
      <Ulice>Herálec 13</Ulice>
      <Psc>58255</Psc>
      <Obec>Herálec</Obec>
      <Stat>Česko</Stat>
      <IsForeigner>false</IsForeigner>
      <IsShortView>false</IsShortView>
      <AllowUnderAge>false</AllowUnderAge>
      <AllowAdult>true</AllowAdult>
      <AllowCheckClient>false</AllowCheckClient>
      <IndexOffset>0</IndexOffset>
      <Index>1</Index>
      <IsVisible>true</IsVisible>
      <IsRequired>true</IsRequired>
      <ClientRelationType>OtherInsured</ClientRelationType>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPOsoba_v3">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id>Osoba-Adult-2</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_Osoba-v3Osoba-Adult-2</IdName>
      <TemplateCode>Osoba-v3</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <TitleAddress>Adresa trvalého bydliště:</TitleAddress>
      <IdentificationNumber />
      <BirthDate xsi:nil="true" />
      <Psc />
      <IsForeigner>false</IsForeigner>
      <IsShortView>false</IsShortView>
      <AllowUnderAge>false</AllowUnderAge>
      <AllowAdult>true</AllowAdult>
      <AllowCheckClient>false</AllowCheckClient>
      <IndexOffset>0</IndexOffset>
      <Index>2</Index>
      <IsVisible>true</IsVisible>
      <IsRequired>false</IsRequired>
      <ClientRelationType>OtherInsured</ClientRelationType>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppZprostredkovatelShort">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Informace o samostatném zprostředkovateli</Title>
      <NumberTitle>3</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_ZprostredkovatelShort</IdName>
      <TemplateCode>ZprostredkovatelShort</TemplateCode>
      <IsUiRelevant>false</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Zprostredkovatel>Broker Trust, a.s. | Hanusova 1411/18, Praha 140 00 | IČ 26439719 | Zapsána v OR u Městského soudu v Praze, sp.zn.B 7141</Zprostredkovatel>
      <OdpovednaOsoba>Ing. Milan Vodička</OdpovednaOsoba>
      <Adresa>Hanusova 1411/18, Praha 140 00</Adresa>
      <Sidlo>Hanusova 1411/18, Praha 140 00</Sidlo>
      <InformaceObchRejstriku>OR u Městského soudu v Praze, sp.zn.B 7141</InformaceObchRejstriku>
      <Telefon>+420 267 912 730</Telefon>
      <Email><EMAIL></Email>
      <Web>www.brokertrust.cz</Web>
      <ICO>26439719</ICO>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppZastupceShort">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Informace o vázaném zástupci, jednajícím jménem Broker Trust, a.s.</Title>
      <NumberTitle>4</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_ZastupceShort</IdName>
      <TemplateCode>ZastupceShort</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <OsobaJmeno>Oleg</OsobaJmeno>
      <OsobaPrijmeni>Test</OsobaPrijmeni>
      <OsobaRegistrace>100131</OsobaRegistrace>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppKlientCileNzp">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Klientem sledované cíle</Title>
      <NumberTitle>5</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_KlientCileNzp</IdName>
      <TemplateCode>KlientCileNzp</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Value>PojistnaUdalost</Value>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppInformaceNzpAuto">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Informace o vozidle</Title>
      <NumberTitle>6</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_InformaceNzpAuto</IdName>
      <TemplateCode>InformaceNzpAuto</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Identifikator>7M72345</Identifikator>
      <TovarniZnacka>Toyota</TovarniZnacka>
      <Model>CH-R</Model>
      <DatumUvedeniDoProvozu>2019-06-03T00:00:00</DatumUvedeniDoProvozu>
      <RocniNajezdKm>20000</RocniNajezdKm>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppPozadavkyNzpAuto">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Požadavky a potřeby klienta</Title>
      <NumberTitle>7</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_PozadavkyNzpAuto</IdName>
      <TemplateCode>PozadavkyNzpAuto</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <PovinneRuceni>true</PovinneRuceni>
      <Limity>Vysoké (150 mil Kč a více)</Limity>
      <LimityValue>HIGH_OVER_150MIL</LimityValue>
      <HavarijniPojisteni>true</HavarijniPojisteni>
      <SpoluUcast>5.000 Kč nebo 5% / min. 5.000 Kč</SpoluUcast>
      <CenaVozidla>500000</CenaVozidla>
      <SpoluUcastValue>EQ_5K_OR_5_PERCENT</SpoluUcastValue>
      <AllRisks>true</AllRisks>
      <Havarie>true</Havarie>
      <Zivel>true</Zivel>
      <StretSeZveri>true</StretSeZveri>
      <Odcizeni>true</Odcizeni>
      <Vandalismus>true</Vandalismus>
      <PoskozeniZviretem>true</PoskozeniZviretem>
      <UrazovePojisteniRidice>true</UrazovePojisteniRidice>
      <Zavazadla>true</Zavazadla>
      <PrimaLikvidace>true</PrimaLikvidace>
      <NahradniVozidlo>true</NahradniVozidlo>
      <PojisteniSkel>true</PojisteniSkel>
      <PojisteniSkelLimit>22000</PojisteniSkelLimit>
      <RozsirenaAsistence>true</RozsirenaAsistence>
      <GAP>true</GAP>
      <GAPLimit>600000</GAPLimit>
      <GaranceCeny>true</GaranceCeny>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppDoporuceneProduktyNzp">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Doporučení poradce</Title>
      <Description>Na základě požadavků a potřeb klienta a s přihlédnutím ke klientovým znalostem poradce doporučil následující produkty.</Description>
      <NumberTitle>8</NumberTitle>
      <Id>DoporuceneProduktyNZP1</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DoporuceneProduktyNzpDoporuceneProduktyNZP1</IdName>
      <TemplateCode>DoporuceneProduktyNzp</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Producer>AXA pojišťovna a.s., člen skupiny UNIQA Insurance Group</Producer>
      <Product>Pojištění auta</Product>
      <ProductId>41a77e2c-6722-498b-8cec-c45a0970e93f</ProductId>
      <ContractType>zmenaSmlouvy</ContractType>
      <ContractSetting>Other</ContractSetting>
      <Substantiation>Doporučuji sjednat smlouvu u pojišťovny AXA, jelikož tento produkt splňuje požadavky klienta na vysoké limity povinného ručení (150 mil Kč a více) a komplexní havarijní pojištění zahrnující AllRisk, což pokrývá havárie, živelní události, střet se zvěří, odcizení, vandalismus i poškození zvířetem. Navíc zahrnuje rozšířenou asistenci, náhradní vozidlo, úrazové pojištění řidiče a cestujících, pojištění skel do limitu 22.000 Kč, a GAP s limitem 600.000 Kč, což zajišťuje vysokou ochranu a bezpečnost.</Substantiation>
      <ClientNotification>Upozornění na nesrovnalosti mezi požadavky klienta a navrhovaným řešením.</ClientNotification>
      <IsVisible>true</IsVisible>
      <IsRequired>true</IsRequired>
      <AttachmentId>71806</AttachmentId>
      <IsGeneratedSubstantiation>true</IsGeneratedSubstantiation>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppDoporuceneProduktyNzp">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id>DoporuceneProduktyNZP2</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DoporuceneProduktyNzpDoporuceneProduktyNZP2</IdName>
      <TemplateCode>DoporuceneProduktyNzp</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <ProductId xsi:nil="true" />
      <ContractSetting>AccordingToClient</ContractSetting>
      <Substantiation />
      <ClientNotification>Neexistují žádné nesrovnalosti mezi požadavky klienta a nabízeným pojištěním.</ClientNotification>
      <IsVisible>false</IsVisible>
      <IsRequired>false</IsRequired>
      <AttachmentId xsi:nil="true" />
      <IsGeneratedSubstantiation>false</IsGeneratedSubstantiation>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppDoporuceneProduktyNzp">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id>DoporuceneProduktyNZP3</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DoporuceneProduktyNzpDoporuceneProduktyNZP3</IdName>
      <TemplateCode>DoporuceneProduktyNzp</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <ProductId xsi:nil="true" />
      <ContractSetting>AccordingToClient</ContractSetting>
      <Substantiation />
      <ClientNotification>Neexistují žádné nesrovnalosti mezi požadavky klienta a nabízeným pojištěním.</ClientNotification>
      <IsVisible>false</IsVisible>
      <IsRequired>false</IsRequired>
      <AttachmentId xsi:nil="true" />
      <IsGeneratedSubstantiation>false</IsGeneratedSubstantiation>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppZprostredkovaneProduktyNzp">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_ZprostredkovaneProduktyNzp</IdName>
      <TemplateCode>ZprostredkovaneProduktyNzp</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Producer>AXA pojišťovna a.s., člen skupiny UNIQA Insurance Group</Producer>
      <Product>Pojištění auta</Product>
      <ProductId>41a77e2c-6722-498b-8cec-c45a0970e93f</ProductId>
      <ProductDisplayName>AXA pojišťovna a.s., člen skupiny UNIQA Insurance Group - Pojištění auta</ProductDisplayName>
      <ClientResigned>false</ClientResigned>
      <ProposedSolution>Ano</ProposedSolution>
      <ContractType>zmenaSmlouvy</ContractType>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPNovaStranka">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_NovaStranka</IdName>
      <TemplateCode>NovaStranka</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EppUpozorneniKlientaPojisteni_3">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Upozornění klienta na dopady navrhovaného pojištění</Title>
      <Description>Klient bere na vědomí následující upozornění, že: &lt;br /&gt;&lt;ul&gt;&lt;li&gt;V případě, že nově sjednaná pojistná smlouva o povinném ručení nenavazuje na klientovu stávající pojistnou smlouvu, vystavuje se klient riziku pokuty ze strany České kanceláře pojistitelů.&lt;br /&gt;&lt;/li&gt;&lt;li&gt;S podpisem pojistné smlouvy mu vzniká &lt;b&gt;povinnost hradit pojistné&lt;/b&gt;.&lt;br /&gt;&lt;/li&gt;&lt;li&gt;V případě neplacení pojistného &lt;b&gt;ztrácí klient pojistnou ochranu, případně mu pojistná ochrana vůbec nevzniká&lt;/b&gt; a vystavuje se riziku &lt;b&gt;vymáhání dluhu na pojistném&lt;/b&gt; ze strany pojišťovny.&lt;br /&gt;&lt;/li&gt;&lt;li&gt;Je si vědom &lt;b&gt;nesrovnalostí specifikovaných&lt;/b&gt; v odstavci níže (pokud taková existují) a tyto akceptuje a dobrovolně uzavírá pojistnou smlouvu k nabízenému pojištění.&lt;br /&gt;&lt;/li&gt;&lt;/ul&gt;Případná další upozornění jsou uvedena v následující tabulce.</Description>
      <NumberTitle>9</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_UpozorneniKlientaPojisteni-3</IdName>
      <TemplateCode>UpozorneniKlientaPojisteni-3</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <DiscrepanciesNotification>V případě, že dojde k prodeji vozidla upozorňuji na nutnost včas oznámit pojišťovně změnu vlastníka vozidla, prodejem nedochází k automatickému ukončení pojištění.Pojištění zaniká až k datu nahlášení změny vlastníka pojistiteli.
Upozorňuji, že sjednané pojištění se nevztahuje na případné pokuty.</DiscrepanciesNotification>
      <DiscrepanciesNotificationDisplayName>Upozornění na dopady navrhovaného pojištění </DiscrepanciesNotificationDisplayName>
      <RefusedByClientVisible>false</RefusedByClientVisible>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPProcesPrijetiTriFaze">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Evidence procesu řešení pojistných potřeb klienta</Title>
      <NumberTitle>10</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_EPPProcesPrijetiTriFaze</IdName>
      <TemplateCode>EPPProcesPrijetiTriFaze</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Date1>2025-03-06T00:00:00</Date1>
      <Date2>2025-06-03T00:00:00</Date2>
      <Date3>2025-06-03T00:00:00</Date3>
      <TextDate1>Poradce představil nabízené služby klientovi a seznámil jej s obsahem informačního listu dne:
                        </TextDate1>
      <TextDate2>Poradce zjistil požadavky a potřeby klienta dne:
                        </TextDate2>
      <TextDate3>Poradce představil klientovi návrh řešení jeho požadavků a potřeb, ze kterých si klient vybral požadované řešení dne:</TextDate3>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPText">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Závěrečná prohlášení</Title>
      <NumberTitle>11</NumberTitle>
      <Id>18</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_Text18</IdName>
      <TemplateCode>Text</TemplateCode>
      <IsUiRelevant>false</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <TextValue>Klient prohlašuje, že všechny jeho výše uvedené požadavky a potřeby jsou jasně, přesně a srozumitelně zaznamenány, výše uvedeným informacím bez výhrady rozumí a nemá k nim žádné další doplňující dotazy. Dále svým podpisem výslovně potvrzuje, že je srozuměn s parametry zprostředkovaného produktu a s jeho principy fungování, pojistnou ochranou a výlukami. Klient současně potvrzuje, že obdržel informační list a že se s ním seznámil.&lt;br /&gt; Obě strany se dohodly, že jazykem jednání je český jazyk. Klient svým podpisem potvrzuje, že převzal kopii tohoto záznamu od poradce.</TextValue>
      <IsVisbleInPdf>true</IsVisbleInPdf>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPPodpisDatumMisto">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_PodpisDatumMisto</IdName>
      <TemplateCode>PodpisDatumMisto</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <Datum xsi:nil="true" />
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPDivStart">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DivStart</IdName>
      <TemplateCode>DivStart</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <DivAttribute>class="pagebreak att"</DivAttribute>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPDivStart">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id>21</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DivStart21</IdName>
      <TemplateCode>DivStart</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
      <DivAttribute>class="att-signatures-doc att-signatures-right att-signatures-right-4x att-signatures-right-4x-Doc"</DivAttribute>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPPodpisOsoba">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Podpis klienta</Title>
      <NumberTitle>0</NumberTitle>
      <Id>PodpisKlient</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_PodpisOsobaPodpisKlient</IdName>
      <TemplateCode>PodpisOsoba</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPPodpisOsoba">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <Title>Podpis VZ</Title>
      <NumberTitle>0</NumberTitle>
      <Id>PodpisPartner</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_PodpisOsobaPodpisPartner</IdName>
      <TemplateCode>PodpisOsoba</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPDivEnd">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id />
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DivEnd</IdName>
      <TemplateCode>DivEnd</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
    </EPPBaseComponent>
    <EPPBaseComponent xsi:type="EPPDivEnd">
      <WithoutValidation>false</WithoutValidation>
      <DocumentId>7c7cf606-a77e-4b33-83fb-0170aa3db048</DocumentId>
      <NumberTitle>0</NumberTitle>
      <Id>25</Id>
      <IdName>7c7cf606-a77e-4b33-83fb-0170aa3db048_DivEnd25</IdName>
      <TemplateCode>DivEnd</TemplateCode>
      <IsUiRelevant>true</IsUiRelevant>
      <IsEnable>true</IsEnable>
    </EPPBaseComponent>
  </Components>
  <PageOrientation>Portrait</PageOrientation>
  <TemplateType>Html</TemplateType>
  <EPPDocumentFooterData>
    <SignerGuid xsi:nil="true" />
    <BopoId>120820</BopoId>
  </EPPDocumentFooterData>
</EPPDocumentData>